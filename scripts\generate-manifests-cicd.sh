#!/bin/bash
#
# Generate ArgoCD and Kubernetes manifests from CI/CD pipeline parameters
# Bash version of generate-manifests-cicd.ps1 for Linux runners
#

set -e

# Default values
APP_TYPE="java-spring-boot"
CONTAINER_PORT=8080
REPLICAS=0
ENABLE_DATABASE=true
HEALTH_CHECK_PATH="/actuator/health"
OUTPUT_DIR="."

# Function to print colored output
print_status() {
    local message="$1"
    local type="${2:-INFO}"
    
    case "$type" in
        "SUCCESS") echo -e "\033[32m[SUCCESS] $message\033[0m" ;;
        "ERROR") echo -e "\033[31m[ERROR] $message\033[0m" ;;
        "WARNING") echo -e "\033[33m[WARNING] $message\033[0m" ;;
        "INFO") echo -e "\033[36m[INFO] $message\033[0m" ;;
        *) echo "$message" ;;
    esac
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Required options:"
    echo "  --app-name NAME          Application name"
    echo "  --project-id ID          Project identifier (lowercase alphanumeric with hyphens)"
    echo "  --environment ENV        Environment (dev, staging, production)"
    echo "  --docker-image IMAGE     Docker image repository"
    echo "  --docker-tag TAG         Docker image tag"
    echo ""
    echo "Optional options:"
    echo "  --source-repo REPO       Source repository"
    echo "  --source-branch BRANCH   Source branch"
    echo "  --commit-sha SHA         Commit SHA"
    echo "  --output-dir DIR         Output directory (default: .)"
    echo "  --app-type TYPE          Application type (default: java-spring-boot)"
    echo "  --container-port PORT    Container port (default: 8080)"
    echo "  --replicas COUNT         Number of replicas (default: auto)"
    echo "  --disable-database       Disable database setup"
    echo "  --health-check-path PATH Health check path (default: /actuator/health)"
    echo "  --help                   Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --app-name \"Auth App\" --project-id \"auth-app\" --environment \"dev\" \\"
    echo "     --docker-image \"srivani8900/auth-app\" --docker-tag \"latest\""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --app-name)
            APP_NAME="$2"
            shift 2
            ;;
        --project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --docker-image)
            DOCKER_IMAGE="$2"
            shift 2
            ;;
        --docker-tag)
            DOCKER_TAG="$2"
            shift 2
            ;;
        --source-repo)
            SOURCE_REPO="$2"
            shift 2
            ;;
        --source-branch)
            SOURCE_BRANCH="$2"
            shift 2
            ;;
        --commit-sha)
            COMMIT_SHA="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --app-type)
            APP_TYPE="$2"
            shift 2
            ;;
        --container-port)
            CONTAINER_PORT="$2"
            shift 2
            ;;
        --replicas)
            REPLICAS="$2"
            shift 2
            ;;
        --disable-database)
            ENABLE_DATABASE=false
            shift
            ;;
        --health-check-path)
            HEALTH_CHECK_PATH="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$APP_NAME" || -z "$PROJECT_ID" || -z "$ENVIRONMENT" || -z "$DOCKER_IMAGE" || -z "$DOCKER_TAG" ]]; then
    print_status "Missing required parameters" "ERROR"
    show_usage
    exit 1
fi

print_status "Starting CI/CD manifest generation..." "INFO"
print_status "Application: $APP_NAME" "INFO"
print_status "Project ID: $PROJECT_ID" "INFO"
print_status "Environment: $ENVIRONMENT" "INFO"
print_status "Docker Image: ${DOCKER_IMAGE}:${DOCKER_TAG}" "INFO"

# Validate project ID format
if [[ ! "$PROJECT_ID" =~ ^[a-z0-9-]+$ ]]; then
    print_status "Invalid project ID format: $PROJECT_ID" "ERROR"
    print_status "Project ID must be lowercase alphanumeric with hyphens only" "ERROR"
    exit 1
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|production|feature)$ ]]; then
    print_status "Invalid environment: $ENVIRONMENT" "ERROR"
    print_status "Supported environments: dev, staging, production, feature" "ERROR"
    exit 1
fi

# Get environment-specific configuration
print_status "Loading environment-specific configuration..." "INFO"

# Set default configuration based on environment
if [[ $REPLICAS -eq 0 ]]; then
    case "$ENVIRONMENT" in
        "dev") REPLICAS=1 ;;
        "staging") REPLICAS=2 ;;
        "production") REPLICAS=3 ;;
        *) REPLICAS=1 ;;
    esac
fi

case "$ENVIRONMENT" in
    "dev")
        MEMORY_REQUEST="256Mi"
        MEMORY_LIMIT="512Mi"
        CPU_REQUEST="100m"
        CPU_LIMIT="500m"
        ;;
    "staging")
        MEMORY_REQUEST="512Mi"
        MEMORY_LIMIT="1Gi"
        CPU_REQUEST="200m"
        CPU_LIMIT="1000m"
        ;;
    "production")
        MEMORY_REQUEST="1Gi"
        MEMORY_LIMIT="2Gi"
        CPU_REQUEST="500m"
        CPU_LIMIT="2000m"
        ;;
    *)
        MEMORY_REQUEST="256Mi"
        MEMORY_LIMIT="512Mi"
        CPU_REQUEST="100m"
        CPU_LIMIT="500m"
        ;;
esac

NAMESPACE="${PROJECT_ID}-${ENVIRONMENT}"
# Use consistent DB_HOST for all environments
DB_HOST="${PROJECT_ID}-postgres"

# Automatically disable database for frontend applications
case "$APP_TYPE" in
    "react-frontend"|"web-app")
        ENABLE_DATABASE=false
        print_status "Automatically disabled database for $APP_TYPE application" "INFO"
        ;;
esac

# Get cluster configuration based on environment
get_cluster_config() {
    local env="$1"
    case "$env" in
        "dev"|"staging")
            CLUSTER_SERVER="https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
            CLUSTER_NAME="doks-target-cluster"
            CLUSTER_ID="6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
            ;;
        "production")
            CLUSTER_SERVER="https://kubernetes.default.svc"
            CLUSTER_NAME="in-cluster"
            CLUSTER_ID="158b6a47-3e7e-4dca-af0f-05a6e07115af"
            ;;
        *)
            # Default to production cluster
            CLUSTER_SERVER="https://kubernetes.default.svc"
            CLUSTER_NAME="in-cluster"
            CLUSTER_ID="158b6a47-3e7e-4dca-af0f-05a6e07115af"
            ;;
    esac
}

# Set cluster configuration
get_cluster_config "$ENVIRONMENT"

print_status "Configuration loaded successfully" "SUCCESS"
print_status "Replicas: $REPLICAS, Memory: $MEMORY_REQUEST-$MEMORY_LIMIT, CPU: $CPU_REQUEST-$CPU_LIMIT" "INFO"
print_status "Cluster: $CLUSTER_NAME ($CLUSTER_ID)" "INFO"

# Prepare template variables
DB_NAME=$(echo "$PROJECT_ID" | tr '-' '_')
CONTAINER_IMAGE="${DOCKER_IMAGE}:${DOCKER_TAG}"

# Additional secret values for consistency with existing templates
JWT_SECRET="supersecretkey"
DB_PASSWORD="password"
SMTP_USER="<EMAIL>"
SMTP_PASS="fqactehafmzlltzz"
GOOGLE_CLIENT_ID="1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

print_status "Template variables prepared" "INFO"

# Create project directory structure
PROJECT_DIR="${OUTPUT_DIR}/${PROJECT_ID}"
ARGOCD_DIR="${PROJECT_DIR}/argocd"
K8S_DIR="${PROJECT_DIR}/k8s"

print_status "Creating project directory structure..." "INFO"
mkdir -p "$ARGOCD_DIR" "$K8S_DIR"

print_status "Project directory created: $PROJECT_DIR" "SUCCESS"
print_status "ArgoCD directory: $ARGOCD_DIR" "INFO"
print_status "Kubernetes directory: $K8S_DIR" "INFO"

# Function to replace template variables in content
replace_template_variables() {
    local content="$1"
    
    # Replace all template variables
    content=$(echo "$content" | sed "s/{{PROJECT_ID}}/$PROJECT_ID/g")
    content=$(echo "$content" | sed "s/{{APP_NAME}}/$APP_NAME/g")
    content=$(echo "$content" | sed "s/{{ENVIRONMENT}}/$ENVIRONMENT/g")
    content=$(echo "$content" | sed "s/{{NAMESPACE}}/$NAMESPACE/g")
    content=$(echo "$content" | sed "s/{{APP_TYPE}}/$APP_TYPE/g")
    content=$(echo "$content" | sed "s|{{CONTAINER_IMAGE}}|$CONTAINER_IMAGE|g")
    content=$(echo "$content" | sed "s/{{CONTAINER_PORT}}/$CONTAINER_PORT/g")
    content=$(echo "$content" | sed "s/{{REPLICAS}}/$REPLICAS/g")
    content=$(echo "$content" | sed "s/{{ENABLE_DATABASE}}/$ENABLE_DATABASE/g")
    content=$(echo "$content" | sed "s|{{HEALTH_CHECK_PATH}}|$HEALTH_CHECK_PATH|g")
    content=$(echo "$content" | sed "s/{{MEMORY_REQUEST}}/$MEMORY_REQUEST/g")
    content=$(echo "$content" | sed "s/{{MEMORY_LIMIT}}/$MEMORY_LIMIT/g")
    content=$(echo "$content" | sed "s/{{CPU_REQUEST}}/$CPU_REQUEST/g")
    content=$(echo "$content" | sed "s/{{CPU_LIMIT}}/$CPU_LIMIT/g")
    content=$(echo "$content" | sed "s/{{DB_USER}}/postgres/g")
    content=$(echo "$content" | sed "s/{{DB_NAME}}/$DB_NAME/g")
    content=$(echo "$content" | sed "s/{{DB_HOST}}/$DB_HOST/g")
    content=$(echo "$content" | sed "s/{{SERVICE_TYPE}}/ClusterIP/g")
    content=$(echo "$content" | sed "s/{{NODE_PORT}}//g")
    content=$(echo "$content" | sed "s/{{INGRESS_ENABLED}}/false/g")
    content=$(echo "$content" | sed "s/{{INGRESS_HOST}}//g")
    content=$(echo "$content" | sed "s|{{INGRESS_PATH}}|/|g")
    content=$(echo "$content" | sed "s/{{SOURCE_REPO}}/$SOURCE_REPO/g")
    content=$(echo "$content" | sed "s/{{SOURCE_BRANCH}}/$SOURCE_BRANCH/g")
    content=$(echo "$content" | sed "s/{{COMMIT_SHA}}/$COMMIT_SHA/g")
    content=$(echo "$content" | sed "s/{{JWT_SECRET}}/$JWT_SECRET/g")
    content=$(echo "$content" | sed "s/{{DB_PASSWORD}}/$DB_PASSWORD/g")
    content=$(echo "$content" | sed "s/{{SMTP_USER}}/$SMTP_USER/g")
    content=$(echo "$content" | sed "s/{{SMTP_PASS}}/$SMTP_PASS/g")
    content=$(echo "$content" | sed "s/{{GOOGLE_CLIENT_ID}}/$GOOGLE_CLIENT_ID/g")
    content=$(echo "$content" | sed "s/{{GOOGLE_CLIENT_SECRET}}/$GOOGLE_CLIENT_SECRET/g")
    # Cluster configuration variables
    content=$(echo "$content" | sed "s|{{CLUSTER_SERVER}}|$CLUSTER_SERVER|g")
    content=$(echo "$content" | sed "s/{{CLUSTER_NAME}}/$CLUSTER_NAME/g")
    content=$(echo "$content" | sed "s/{{CLUSTER_ID}}/$CLUSTER_ID/g")

    echo "$content"
}

# Function to process template files
process_template() {
    local template_file="$1"
    local output_file="$2"

    if [[ ! -f "$template_file" ]]; then
        print_status "Template file not found: $template_file" "WARNING"
        return 1
    fi

    print_status "Processing template: $(basename "$template_file")" "INFO"

    # Read template content
    local content
    content=$(cat "$template_file")

    # Handle conditional blocks for database
    if [[ "$ENABLE_DATABASE" == "true" ]]; then
        # Remove conditional markers but keep content
        content=$(echo "$content" | sed 's/{{#if ENABLE_DATABASE}}//g')
        content=$(echo "$content" | sed 's/{{\/if}}//g')
    else
        # Remove entire conditional blocks
        content=$(echo "$content" | sed '/{{#if ENABLE_DATABASE}}/,/{{\/if}}/d')
    fi

    # Replace template variables
    content=$(replace_template_variables "$content")

    # Write to output file
    echo "$content" > "$output_file"

    print_status "Generated: $output_file" "SUCCESS"
}

# Generate manifests from templates
print_status "Generating manifests from templates..." "INFO"

TEMPLATES_DIR="templates"
if [[ ! -d "$TEMPLATES_DIR" ]]; then
    print_status "Templates directory not found: $TEMPLATES_DIR" "ERROR"
    exit 1
fi

# Generate ArgoCD manifests
print_status "Generating ArgoCD manifests..." "INFO"
process_template "$TEMPLATES_DIR/argocd/project.yaml" "$ARGOCD_DIR/project.yaml"
process_template "$TEMPLATES_DIR/argocd/application.yaml" "$ARGOCD_DIR/application.yaml"

# Generate Kubernetes manifests
print_status "Generating Kubernetes manifests..." "INFO"
process_template "$TEMPLATES_DIR/k8s/namespace.yaml" "$K8S_DIR/namespace.yaml"
process_template "$TEMPLATES_DIR/k8s/deployment.yaml" "$K8S_DIR/deployment.yaml"
process_template "$TEMPLATES_DIR/k8s/service.yaml" "$K8S_DIR/service.yaml"
process_template "$TEMPLATES_DIR/k8s/configmap.yaml" "$K8S_DIR/configmap.yaml"
process_template "$TEMPLATES_DIR/k8s/secret.yaml" "$K8S_DIR/secret.yaml"

# Generate database manifests if enabled
if [[ "$ENABLE_DATABASE" == "true" ]]; then
    print_status "Generating database manifests..." "INFO"
    process_template "$TEMPLATES_DIR/k8s/postgres-deployment.yaml" "$K8S_DIR/postgres-deployment.yaml"
    process_template "$TEMPLATES_DIR/k8s/postgres-service.yaml" "$K8S_DIR/postgres-service.yaml"
    # Note: postgres-configmap.yaml and postgres-secret.yaml might not exist in templates
    if [[ -f "$TEMPLATES_DIR/k8s/postgres-configmap.yaml" ]]; then
        process_template "$TEMPLATES_DIR/k8s/postgres-configmap.yaml" "$K8S_DIR/postgres-configmap.yaml"
    fi
    if [[ -f "$TEMPLATES_DIR/k8s/postgres-secret.yaml" ]]; then
        process_template "$TEMPLATES_DIR/k8s/postgres-secret.yaml" "$K8S_DIR/postgres-secret.yaml"
    fi
    process_template "$TEMPLATES_DIR/k8s/postgres-pvc.yaml" "$K8S_DIR/postgres-pvc.yaml"
fi

# Validate generated YAML files
print_status "Validating generated YAML files..." "INFO"
yaml_valid=true

for yaml_file in "$PROJECT_DIR"/**/*.yaml; do
    if [[ -f "$yaml_file" ]]; then
        if command -v yamllint >/dev/null 2>&1; then
            if ! yamllint "$yaml_file" >/dev/null 2>&1; then
                print_status "YAML validation failed: $yaml_file" "WARNING"
                yaml_valid=false
            fi
        else
            # Basic YAML syntax check using python if yamllint is not available
            if command -v python3 >/dev/null 2>&1; then
                if ! python3 -c "import yaml; yaml.safe_load(open('$yaml_file'))" >/dev/null 2>&1; then
                    print_status "YAML validation failed: $yaml_file" "WARNING"
                    yaml_valid=false
                fi
            fi
        fi
    fi
done

if [[ "$yaml_valid" == "true" ]]; then
    print_status "All YAML files validated successfully" "SUCCESS"
else
    print_status "Some YAML files failed validation - please check manually" "WARNING"
fi

# List generated files
print_status "Generated files:" "INFO"
find "$PROJECT_DIR" -type f -name "*.yaml" | sort | while read -r file; do
    print_status "  $(realpath --relative-to="$OUTPUT_DIR" "$file")" "INFO"
done

print_status "Manifest generation completed successfully!" "SUCCESS"
print_status "Project directory: $PROJECT_DIR" "INFO"
print_status "ArgoCD manifests: $ARGOCD_DIR" "INFO"
print_status "Kubernetes manifests: $K8S_DIR" "INFO"

exit 0
