apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: react-frontend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend
      app.kubernetes.io/name: ai-react-frontend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-react-frontend
        app.kubernetes.io/name: ai-react-frontend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: react-frontend
        version: v1.0.0
        environment: dev
    spec:
      # React Frontend - No init containers needed (stateless)
      containers:
      - name: ai-react-frontend
        image: registry.digitalocean.com/doks-registry/ai-react-frontend:arc-runners
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-react-frontend-config
        # React Frontend - Configure nginx to listen on port 3000
        env:
        - name: NGINX_PORT
          value: "3000"
        - name: LISTEN_PORT
          value: "3000"
        # Health Checks - Application Type Specific
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
