apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: react-frontend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-react-frontend
      app.kubernetes.io/name: ai-react-frontend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-react-frontend
        app.kubernetes.io/name: ai-react-frontend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: react-frontend
        version: v1.0.0
        environment: dev
    spec:
      # React Frontend - No init containers needed (stateless)
      containers:
      - name: ai-react-frontend
        image: registry.digitalocean.com/doks-registry/ai-react-frontend:arc-runners
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-react-frontend-config
        # React Frontend - Minimal environment variables (build-time configs in ConfigMap)
        env: []
        # Health Checks - Application Type Specific (TCP instead of HTTP)
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
