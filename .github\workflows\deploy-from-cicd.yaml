name: 🚀 Deploy from CI/CD Pipeline

on:
  repository_dispatch:
    types: [deploy-to-argocd]

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

jobs:
  validate-dispatch:
    runs-on: [self-hosted, Linux]
    outputs:
      should-deploy: ${{ steps.validate.outputs.should-deploy }}
      app-name: ${{ steps.validate.outputs.app-name }}
      project-id: ${{ steps.validate.outputs.project-id }}
      application-type: ${{ steps.validate.outputs.application-type }}
      environment: ${{ steps.validate.outputs.environment }}
      docker-image: ${{ steps.validate.outputs.docker-image }}
      docker-tag: ${{ steps.validate.outputs.docker-tag }}
      container-port: ${{ steps.validate.outputs.container-port }}
      health-check-path: ${{ steps.validate.outputs.health-check-path }}
      source-repo: ${{ steps.validate.outputs.source-repo }}
      source-branch: ${{ steps.validate.outputs.source-branch }}
      commit-sha: ${{ steps.validate.outputs.commit-sha }}
    steps:
      - name: 🔍 Validate Dispatch Payload
        id: validate
        run: |
          echo "=== REPOSITORY DISPATCH VALIDATION ==="
          echo "Event type: ${{ github.event.action }}"
          echo "Client payload: ${{ toJson(github.event.client_payload) }}"
          echo "=================================="

          # Extract required payload data
          APP_NAME="${{ github.event.client_payload.app_name }}"
          PROJECT_ID="${{ github.event.client_payload.project_id }}"
          ENVIRONMENT="${{ github.event.client_payload.environment }}"
          DOCKER_IMAGE="${{ github.event.client_payload.docker_image }}"
          DOCKER_TAG="${{ github.event.client_payload.docker_tag }}"

          # Extract optional payload data with defaults
          APPLICATION_TYPE="${{ github.event.client_payload.application_type }}"
          CONTAINER_PORT="${{ github.event.client_payload.container_port }}"
          # Note: HEALTH_CHECK_PATH is automatically set based on APPLICATION_TYPE below

          # Extract tracking data (optional)
          SOURCE_REPO="${{ github.event.client_payload.source_repo }}"
          SOURCE_BRANCH="${{ github.event.client_payload.source_branch }}"
          COMMIT_SHA="${{ github.event.client_payload.commit_sha }}"

          # Validate required fields
          if [ -z "$APP_NAME" ] || [ -z "$PROJECT_ID" ] || [ -z "$ENVIRONMENT" ] || [ -z "$DOCKER_IMAGE" ] || [ -z "$DOCKER_TAG" ]; then
            echo "❌ Missing required fields in dispatch payload"
            echo "Required: app_name, project_id, environment, docker_image, docker_tag"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Set default application type if not provided
          if [ -z "$APPLICATION_TYPE" ]; then
            APPLICATION_TYPE="web-app"
            echo "⚠️ No application_type provided, defaulting to: $APPLICATION_TYPE"
          fi

          # Validate project ID format (lowercase alphanumeric with hyphens)
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate environment
          if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
            echo "❌ Invalid environment: $ENVIRONMENT"
            echo "Supported environments: dev, staging, production"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate application type
          case "$APPLICATION_TYPE" in
            "react-frontend"|"springboot-backend"|"web-app"|"api"|"microservice"|"worker"|"database")
              echo "✅ Valid application type: $APPLICATION_TYPE"
              ;;
            *)
              echo "❌ Invalid application type: $APPLICATION_TYPE"
              echo "Supported types: react-frontend, springboot-backend, web-app, api, microservice, worker, database"
              echo "should-deploy=false" >> $GITHUB_OUTPUT
              exit 0
              ;;
          esac

          # Set type-specific defaults (automatically based on application type)
          case "$APPLICATION_TYPE" in
            "react-frontend")
              CONTAINER_PORT="${CONTAINER_PORT:-3000}"  # React apps typically run on port 3000
              HEALTH_CHECK_PATH="/"  # Always use / for React frontends
              ;;
            "springboot-backend")
              CONTAINER_PORT="${CONTAINER_PORT:-8080}"
              HEALTH_CHECK_PATH="/actuator/health"  # Always use /actuator/health for Spring Boot
              ;;
            *)
              CONTAINER_PORT="${CONTAINER_PORT:-8080}"
              HEALTH_CHECK_PATH="/health"  # Default for other types
              ;;
          esac

          echo "✅ Dispatch payload validation passed"
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "app-name=$APP_NAME" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "application-type=$APPLICATION_TYPE" >> $GITHUB_OUTPUT
          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "docker-image=$DOCKER_IMAGE" >> $GITHUB_OUTPUT
          echo "docker-tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
          echo "container-port=$CONTAINER_PORT" >> $GITHUB_OUTPUT
          echo "health-check-path=$HEALTH_CHECK_PATH" >> $GITHUB_OUTPUT
          echo "source-repo=$SOURCE_REPO" >> $GITHUB_OUTPUT
          echo "source-branch=$SOURCE_BRANCH" >> $GITHUB_OUTPUT
          echo "commit-sha=$COMMIT_SHA" >> $GITHUB_OUTPUT

  generate-manifests:
    needs: validate-dispatch
    if: needs.validate-dispatch.outputs.should-deploy == 'true'
    runs-on: [self-hosted, Linux]
    outputs:
      generation-success: ${{ steps.generate.outputs.success }}
      project-path: ${{ steps.generate.outputs.project-path }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: � Generate ArgoCD Manifests
        id: generate
        shell: bash
        run: |
          echo "🔍 Using Python script for manifest generation..."

          # Run the Python script
          python scripts/generate-manifests-cicd.py \
            --app-name "${{ needs.validate-dispatch.outputs.app-name }}" \
            --project-id "${{ needs.validate-dispatch.outputs.project-id }}" \
            --application-type "${{ needs.validate-dispatch.outputs.application-type }}" \
            --environment "${{ needs.validate-dispatch.outputs.environment }}" \
            --docker-image "${{ needs.validate-dispatch.outputs.docker-image }}" \
            --docker-tag "${{ needs.validate-dispatch.outputs.docker-tag }}" \
            --container-port "${{ needs.validate-dispatch.outputs.container-port }}" \
            --health-check-path "${{ needs.validate-dispatch.outputs.health-check-path }}" \
            --source-repo "${{ needs.validate-dispatch.outputs.source-repo }}" \
            --source-branch "${{ needs.validate-dispatch.outputs.source-branch }}" \
            --commit-sha "${{ needs.validate-dispatch.outputs.commit-sha }}" \
            --output-dir "."

          status=$?
          if [ $status -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "project-path=${{ needs.validate-dispatch.outputs.project-id }}" >> $GITHUB_OUTPUT
            echo "✅ Manifest generation completed successfully"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Manifest generation failed with exit code: $status"
            exit 1
          fi

      - name: � Setup Python Dependencies
        if: steps.generate.outputs.success == 'true'
        shell: bash
        run: |
          echo "🐍 Installing Python dependencies..."
          # Install PyYAML for YAML validation
          pip3 install PyYAML --user --quiet || echo "⚠️ PyYAML installation failed, will use basic validation"

      - name: �🔍 Validate Generated Files
        if: steps.generate.outputs.success == 'true'
        shell: bash
        run: |
          PROJECT_ID="${{ needs.validate-dispatch.outputs.project-id }}"

          echo "🔍 Validating generated files for: $PROJECT_ID"

          # Check if project directory exists
          if [ ! -d "$PROJECT_ID" ]; then
            echo "❌ Project directory not found: $PROJECT_ID"
            exit 1
          fi

          # Check required files
          REQUIRED_FILES=(
            "$PROJECT_ID/argocd/project.yaml"
            "$PROJECT_ID/argocd/application.yaml"
            "$PROJECT_ID/k8s/namespace.yaml"
            "$PROJECT_ID/k8s/deployment.yaml"
            "$PROJECT_ID/k8s/service.yaml"
            "$PROJECT_ID/k8s/configmap.yaml"
            "$PROJECT_ID/k8s/secret.yaml"
          )

          for file in "${REQUIRED_FILES[@]}"; do
            if [ ! -f "$file" ]; then
              echo "❌ Required file missing: $file"
              exit 1
            fi
            echo "✅ Found: $file"
          done

          # Validate YAML syntax using Python
          echo "🔍 Validating YAML syntax..."
          python3 scripts/validate-yaml.py "$PROJECT_ID"

          echo "✅ File validation completed"

      - name: 📁 List Generated Files
        if: steps.generate.outputs.success == 'true'
        shell: bash
        run: |
          PROJECT_ID="${{ needs.validate-dispatch.outputs.project-id }}"
          echo "📁 Generated project structure:"
          tree "$PROJECT_ID" || find "$PROJECT_ID" -type f | sort

      - name: 🔧 Configure Git
        if: steps.generate.outputs.success == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: 💾 Commit Generated Files
        id: commit
        if: steps.generate.outputs.success == 'true'
        run: |
          PROJECT_ID="${{ needs.validate-dispatch.outputs.project-id }}"
          APP_NAME="${{ needs.validate-dispatch.outputs.app-name }}"
          ENVIRONMENT="${{ needs.validate-dispatch.outputs.environment }}"
          DOCKER_TAG="${{ needs.validate-dispatch.outputs.docker-tag }}"
          SOURCE_REPO="${{ needs.validate-dispatch.outputs.source-repo }}"
          COMMIT_SHA="${{ needs.validate-dispatch.outputs.commit-sha }}"
          
          # Add generated files
          git add "$PROJECT_ID/"
          
          # Check if there are changes to commit
          if git diff --staged --quiet; then
            echo "No changes to commit"
            echo "committed=false" >> $GITHUB_OUTPUT
          else
            # Commit changes
            git commit -m "🚀 Deploy $APP_NAME ($PROJECT_ID) to $ENVIRONMENT from CI/CD

            Triggered by merge to main in $SOURCE_REPO

            - Application Type: ${{ needs.validate-dispatch.outputs.application-type }}
            - Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:$DOCKER_TAG
            - Container Port: ${{ needs.validate-dispatch.outputs.container-port }}
            - Health Check: ${{ needs.validate-dispatch.outputs.health-check-path }}
            - Environment: $ENVIRONMENT
            - Source Commit: $COMMIT_SHA
            - ArgoCD Application and Project manifests
            - Complete Kubernetes deployment manifests

            Auto-generated by GitOps CI/CD integration"
            
            echo "committed=true" >> $GITHUB_OUTPUT
            echo "✅ Changes committed successfully"
          fi

      - name: 🚀 Push Changes
        if: steps.commit.outputs.committed == 'true'
        run: |
          git push origin main
          echo "✅ Changes pushed to main branch"

  deploy-to-argocd:
    needs: [validate-dispatch, generate-manifests]
    if: needs.generate-manifests.outputs.generation-success == 'true'
    runs-on: [self-hosted, Linux]
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: 🔧 Setup kubectl for ArgoCD Management Cluster
        id: setup-kubectl
        continue-on-error: true
        run: |
          echo "🔧 Setting up kubectl for ArgoCD Management cluster..."

          # Check if auto-deployment is enabled
          AUTO_DEPLOY_ENABLED="${{ vars.ENABLE_AUTO_DEPLOY }}"
          if [ "$AUTO_DEPLOY_ENABLED" != "true" ]; then
            echo "auto-deploy-enabled=false" >> $GITHUB_OUTPUT
            echo "ℹ️ Auto-deployment is disabled (ENABLE_AUTO_DEPLOY != true)"
            exit 0
          fi
          echo "auto-deploy-enabled=true" >> $GITHUB_OUTPUT

          # Check if DIGITALOCEAN_ACCESS_TOKEN is set
          if [ -z "$DIGITALOCEAN_ACCESS_TOKEN" ]; then
            echo "❌ DIGITALOCEAN_ACCESS_TOKEN is not set"
            echo "Please add your DigitalOcean access token as a repository secret"
            echo "auto-deploy-enabled=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Install doctl if not available
          if ! command -v doctl >/dev/null 2>&1; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q "https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz"
            tar xf "doctl-1.104.0-linux-amd64.tar.gz"
            sudo mv doctl /usr/local/bin/
            sudo chmod +x /usr/local/bin/doctl
            rm -f "doctl-1.104.0-linux-amd64.tar.gz"
            echo "✅ doctl installed successfully"
          else
            echo "✅ doctl is already available"
          fi

          # Install kubectl if not available
          if ! command -v kubectl >/dev/null 2>&1; then
            echo "📦 Installing kubectl..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            echo "✅ kubectl installed successfully"
          else
            echo "✅ kubectl is already available"
          fi

          echo "kubectl-available=true" >> $GITHUB_OUTPUT

          # Authenticate with DigitalOcean
          echo "🔐 Authenticating with DigitalOcean..."
          doctl auth init --access-token "$DIGITALOCEAN_ACCESS_TOKEN"

          # Configure kubectl for ArgoCD Management Cluster
          # ArgoCD always runs on the management cluster regardless of target environment
          CLUSTER_ID="158b6a47-3e7e-4dca-af0f-05a6e07115af"
          ENVIRONMENT="${{ needs.validate-dispatch.outputs.environment }}"
          echo "⚙️  Configuring kubectl for ArgoCD Management cluster: $CLUSTER_ID"
          echo "🎯 Target environment: $ENVIRONMENT (applications will be deployed to target cluster via ArgoCD)"

          doctl kubernetes cluster kubeconfig save "$CLUSTER_ID"

          # Test cluster connectivity
          if kubectl cluster-info >/dev/null 2>&1; then
            echo "cluster-accessible=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD Management cluster is accessible"
            kubectl cluster-info
          else
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD Management cluster is not accessible"
            exit 0
          fi

          # Check ArgoCD availability
          if kubectl get namespace argocd >/dev/null 2>&1; then
            echo "argocd-available=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD namespace exists"
          else
            echo "argocd-available=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD namespace not found"
            exit 0
          fi

          echo "✅ All prerequisites met for auto-deployment"

      - name: 🚀 Auto-Deploy to ArgoCD
        id: auto-deploy
        if: steps.setup-kubectl.outputs.auto-deploy-enabled == 'true' && steps.setup-kubectl.outputs.cluster-accessible == 'true' && steps.setup-kubectl.outputs.argocd-available == 'true'
        continue-on-error: true
        run: |
          PROJECT_ID="${{ needs.validate-dispatch.outputs.project-id }}"
          APP_NAME="${{ needs.validate-dispatch.outputs.app-name }}"
          ENVIRONMENT="${{ needs.validate-dispatch.outputs.environment }}"
          DOCKER_IMAGE="${{ needs.validate-dispatch.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-dispatch.outputs.docker-tag }}"

          echo "🚀 Starting automated ArgoCD deployment for: $PROJECT_ID"
          echo "📋 Application: $APP_NAME"
          echo "🌍 Environment: $ENVIRONMENT"
          echo "🐳 Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"

          # Initialize deployment status
          DEPLOYMENT_SUCCESS=false
          DEPLOYMENT_ERROR=""

          # Pull latest changes to ensure we have the generated manifests
          git pull origin main

          # Verify manifest files exist
          PROJECT_FILE="$PROJECT_ID/argocd/project.yaml"
          APPLICATION_FILE="$PROJECT_ID/argocd/application.yaml"

          if [ ! -f "$PROJECT_FILE" ] || [ ! -f "$APPLICATION_FILE" ]; then
            DEPLOYMENT_ERROR="ArgoCD manifest files not found: $PROJECT_FILE, $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "✅ Manifest files found"
          echo "📋 Project file: $PROJECT_FILE"
          echo "🎯 Application file: $APPLICATION_FILE"

          # Install PyYAML if not available
          echo "🐍 Ensuring Python dependencies..."
          pip3 install PyYAML --user --quiet || echo "⚠️ PyYAML installation failed, will use basic validation"

          # Validate YAML syntax using Python
          echo "🔍 Validating YAML syntax..."
          python3 scripts/validate-yaml.py "$PROJECT_FILE" "$APPLICATION_FILE"
          if [ $? -ne 0 ]; then
            DEPLOYMENT_ERROR="YAML validation failed for ArgoCD manifests"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "✅ Manifest files validated successfully"

          # Step 2: Apply ArgoCD Project
          echo "📋 Applying ArgoCD Project..."
          if kubectl apply -f "$PROJECT_FILE"; then
            echo "✅ ArgoCD Project applied successfully"
          else
            DEPLOYMENT_ERROR="Failed to apply ArgoCD Project: $PROJECT_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 3: Apply ArgoCD Application
          echo "🎯 Applying ArgoCD Application..."
          if kubectl apply -f "$APPLICATION_FILE"; then
            echo "✅ ArgoCD Application applied successfully"
            DEPLOYMENT_SUCCESS=true
          else
            DEPLOYMENT_ERROR="Failed to apply ArgoCD Application: $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 4: Wait for application to be created in ArgoCD
          echo "⏳ Waiting for ArgoCD application to be created..."
          for i in {1..30}; do
            if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
              echo "✅ ArgoCD application '$PROJECT_ID' created successfully"
              break
            fi
            echo "⏳ Waiting for application creation... ($i/30)"
            sleep 2
          done

          # Step 5: Trigger sync if application exists
          if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
            echo "🔄 Triggering ArgoCD application sync..."
            if kubectl patch application "$PROJECT_ID" -n argocd --type merge -p '{"operation":{"sync":{}}}' 2>/dev/null || true; then
              echo "✅ ArgoCD sync triggered"
            else
              echo "ℹ️ Manual sync may be required in ArgoCD dashboard"
            fi
          fi

          if [ "$DEPLOYMENT_SUCCESS" = true ]; then
            echo "deployment-success=true" >> $GITHUB_OUTPUT
            echo "🎉 ArgoCD deployment completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  • Project: $PROJECT_ID"
            echo "  • Application: $APP_NAME"
            echo "  • Environment: $ENVIRONMENT"
            echo "  • Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
            echo "  • ArgoCD Dashboard: Check your ArgoCD UI for deployment status"
          else
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
          fi

      - name: 🎉 Success Notification
        if: steps.auto-deploy.outputs.deployment-success == 'true'
        run: |
          echo "🎉 CI/CD-triggered deployment completed successfully!"
          echo ""
          echo "📊 Deployment Details:"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Project ID: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Environment: ${{ needs.validate-dispatch.outputs.environment }}"
          echo "  • Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          echo "  • Source Repository: ${{ needs.validate-dispatch.outputs.source-repo }}"
          echo "  • Source Branch: ${{ needs.validate-dispatch.outputs.source-branch }}"
          echo "  • Commit SHA: ${{ needs.validate-dispatch.outputs.commit-sha }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor deployment in ArgoCD dashboard"
          echo "  • Verify application health in Kubernetes cluster"
          echo "  • Check application logs if needed"

      - name: ❌ Deployment Failed
        if: failure() || (steps.setup-kubectl.outputs.auto-deploy-enabled == 'true' && steps.auto-deploy.outputs.deployment-success != 'true')
        run: |
          echo "❌ CI/CD-triggered deployment failed"
          echo ""
          echo "📊 Deployment Details:"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Project ID: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Environment: ${{ needs.validate-dispatch.outputs.environment }}"
          echo "  • Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          echo ""
          echo "🔍 Troubleshooting:"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify ArgoCD cluster connectivity"
          echo "  • Ensure ENABLE_AUTO_DEPLOY repository variable is set to 'true'"
          echo "  • Check generated manifest files for syntax errors"
          echo ""
          echo "🛠️ Manual Deployment:"
          echo "  kubectl apply -f ${{ needs.validate-dispatch.outputs.project-id }}/argocd/project.yaml"
          echo "  kubectl apply -f ${{ needs.validate-dispatch.outputs.project-id }}/argocd/application.yaml"
